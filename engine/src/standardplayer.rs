#![allow(dead_code,unused_imports)]

use crate::cards::{Rank, Suit, Card};
use crate::donkey::{Move, Transition, VisibleState, new_game_det};
use crate::players::{Player, DumbPlayer};

use num::FromPrimitive;
use std::collections::HashSet;

use log::{info, error};

pub struct TabularState<const N: usize> {
    consistency_matrix: [[bool; 6]; N],
}

impl<const N: usize> TabularState<N> {
    pub fn new() -> TabularState<N> {
        TabularState {
            consistency_matrix: [[false; 6]; N],
        }
    }

    pub fn to_table_string(&self) -> String {
        let mut s = String::with_capacity(N*11);
        for i in 0..N {
            let card = Card::from_index(i, N);
            s.push_str(&card.to_repr());
            s.push_str(": ");
            for j in 0..6 {
                if self.consistency_matrix[i][j] {
                    s.push('1');
                }
                else {
                    s.push('0');
                }
            }
            s.push('\n');
        }
        s
    }
}

pub struct StandardPlayer<const N: usize> {
    tabular_state: TabularState<40>
}

impl<const N: usize> StandardPlayer<N> {
    pub fn new() -> StandardPlayer<N> {
        StandardPlayer {
            tabular_state: TabularState::new()
        }
    }
}

impl<const N: usize> StandardPlayer<N> {
    fn _check_validity(&self, vs: &VisibleState) -> bool {
        let mat = &self.tabular_state.consistency_matrix;
        
        let is_index = |target: usize, vals: &[bool]| -> bool {
            (0..6).all(|j| vals[j] == (j == target))
        };

        // anything in hand should be consistent in matrix.
        let hand_indices: HashSet<usize> = vs.hand
            .iter()
            .map(|x| x.to_index(N) )
            .collect();
        let hand_good = hand_indices.iter().all(|&idx| is_index(0, &(mat[idx])));

        // anything in trick should be consistent in matrix.
        let trick_indices: HashSet<usize> = vs.cur_trick
            .iter()
            .map(|x| x.card.to_index(N))
            .collect();
        let trick_good = trick_indices.iter().all(|&idx| is_index(4, &(mat[idx])));

        // everything else should be consistent with being with another player.
        let rest_good = (0..N).all(|idx| {
            if !hand_indices.contains(&idx) && !trick_indices.contains(&idx) {
                let vals = &(mat[idx]);
                let cleared = vals.iter().enumerate().all( |(j, &v)| v == (j == 5));
                let unknown = !vals[0] && !vals[4] && !vals[5] && (vals[1] || vals[2] || vals[3]);
                cleared || unknown
            }
            else {
                true
            }
        });

        hand_good && trick_good && rest_good
    }


    fn update_deal(&mut self, vs: &VisibleState) {
        assert_eq!(vs.cur_trick.len(), 0);
        let hand_indices = vs.hand.iter().map(|x| x.to_index(N) ).collect::<HashSet<_>>();
        for idx in 0..N {
            if hand_indices.contains(&idx) {
                for j in 0..6 {
                    self.tabular_state.consistency_matrix[idx][j] = j == 0;
                }
            }
            else {
                for j in 0..6 {
                    self.tabular_state.consistency_matrix[idx][j] = (1..=3).contains(&j);
                }
            }
        }
    }

    fn update_play(&mut self, card_value: u16) {
        let idx = Card::from_value(card_value as usize).to_index(N);
        for j in 0..6 {
            self.tabular_state.consistency_matrix[idx][j] = j == 4;
        }
    }

    fn move_trick(&mut self, dest: usize) {
        for idx in 0..N {
            if self.tabular_state.consistency_matrix[idx][4] {
                self.tabular_state.consistency_matrix[idx][4] = false;
                self.tabular_state.consistency_matrix[idx][dest] = true; 
            }
        }
    }

    fn update_clear(&mut self) {
        self.move_trick(5);
    }

    fn update_play_take(&mut self, player_id: u8, card_id: u16, taker_id: u8) {
        let suit_length = N / 4;
        // person cutting does not have that suit anymore.
        let trick_suit = (0..N).find(|x| self.tabular_state.consistency_matrix[*x][4])
            .unwrap() / suit_length;
        let start_idx = trick_suit * suit_length;
        for idx in start_idx..start_idx+suit_length {
            self.tabular_state.consistency_matrix[idx][player_id as usize] = false;
        }

        self.update_play(card_id);
        self.move_trick(taker_id as usize);
    }
}

impl<const N: usize> Player for StandardPlayer<N> {
    fn update_state(&mut self, vs: &VisibleState, last_transition: &Transition) {
        match last_transition {
            Transition::Deal => self.update_deal(vs),
            Transition::Play(_pid, card) => self.update_play(*card),
            Transition::PlayClear(_pid, card) => {
                self.update_play(*card);
                self.update_clear();
            },
            Transition::PlayTake(pid, card, tid) => self.update_play_take(*pid, *card, *tid)
        };

        let valid = self._check_validity(vs);
        if !valid {
            error!("Improper consistency matrix: \n{}", self.tabular_state.to_table_string());
        }
        else {
            info!("Consistency matrix: \n{}", self.tabular_state.to_table_string());
        }
    }

    // Get next move when it is this player's turn.
    fn get_move(&mut self, vs: &VisibleState) -> Move {
        let mut p = DumbPlayer{};
        p.get_move(vs)
    }
}


#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn init_edit_test() {
        let mut x: TabularState<40> = TabularState::new();
        x.consistency_matrix[39][5] = true;
    }

    #[test]
    fn update_deal_test() {
        let mut data: StandardPlayer<40> = StandardPlayer::new();
        let state = new_game_det(42);
        let vs = state.get_visible_state(0).unwrap();
        data.update_deal(&vs);
        for card in vs.hand.iter() {
            let idx = card.to_index(40);
            assert_eq!(data.tabular_state.consistency_matrix[idx][0], true);
            for j in 1..6 {
                assert_eq!(data.tabular_state.consistency_matrix[idx][j], false);
            }
        }
    }

    #[test]
    fn update_tests() {
        //TODO: Need to write this test.
    }
}